import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const app = express();
const PORT = process.env.API_PORT || 3001;

console.log("SPOTIFY_CLIENT_ID:", process.env.SPOTIFY_CLIENT_ID);

// Enable CORS for your frontend
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));

app.use(express.json());

// Cache structure
let trackCache = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Check if cache is valid
function isCacheValid() {
  if (!trackCache) return false;
  const now = Date.now();
  return now - trackCache.timestamp < CACHE_DURATION;
}

// Refresh Spotify access token
async function refreshAccessToken() {
  const refresh_token = process.env.SPOTIFY_REFRESH_TOKEN;
  const client_id = process.env.SPOTIFY_CLIENT_ID;
  const client_secret = process.env.SPOTIFY_CLIENT_SECRET;

  if (!refresh_token || !client_id || !client_secret) {
    throw new Error('Missing Spotify credentials in environment variables');
  }

  const response = await fetch('https://accounts.spotify.com/api/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${Buffer.from(`${client_id}:${client_secret}`).toString('base64')}`
    },
    body: new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refresh_token
    })
  });

  if (!response.ok) {
    throw new Error(`Failed to refresh token: ${response.statusText}`);
  }

  const data = await response.json();
  return data.access_token;
}

// Get recently played track from Spotify
async function getRecentlyPlayedTrack(accessToken) {
  const response = await fetch('https://api.spotify.com/v1/me/player/recently-played?limit=1', {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });

  if (!response.ok) {
    if (response.status === 401) {
      // Token expired, try refreshing
      const newToken = await refreshAccessToken();
      return getRecentlyPlayedTrack(newToken);
    }
    throw new Error(`Failed to fetch recently played tracks: ${response.statusText}`);
  }

  const data = await response.json();
  return data.items[0] || null;
}

// Get current playing track from Spotify
async function getCurrentlyPlaying(accessToken) {
  const response = await fetch('https://api.spotify.com/v1/me/player/currently-playing', {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });

  if (response.status === 204) {
    // No track currently playing
    return null;
  }

  if (!response.ok) {
    if (response.status === 401) {
      // Token expired, try refreshing
      const newToken = await refreshAccessToken();
      return getCurrentlyPlaying(newToken);
    }
    throw new Error(`Failed to fetch currently playing track: ${response.statusText}`);
  }

  const data = await response.json();
  return data;
}

// Format track data for frontend
function formatTrackData(track, isCurrentlyPlaying = false) {
  if (!track) return null;

  const trackItem = isCurrentlyPlaying ? track.item : track.track;
  
  return {
    title: trackItem.name,
    artist: trackItem.artists.map(artist => artist.name).join(', '),
    album: trackItem.album.name,
    albumArt: trackItem.album.images[0]?.url || null,
    spotifyUrl: trackItem.external_urls.spotify,
    isPlaying: isCurrentlyPlaying && track.is_playing,
    playedAt: isCurrentlyPlaying ? new Date().toISOString() : track.played_at
  };
}

// API endpoint for Spotify data
app.get('/api/spotify', async (req, res) => {
  try {
    // Return cached data if valid
    if (isCacheValid() && trackCache) {
      console.log('Returning cached Spotify track data');
      return res.json(trackCache.data);
    }

    console.log('Fetching fresh Spotify track data');
    
    // Get access token
    const accessToken = await refreshAccessToken();
    
    // Try to get currently playing track first
    let currentTrack = await getCurrentlyPlaying(accessToken);
    let formattedTrack = null;
    
    if (currentTrack && currentTrack.item) {
      formattedTrack = formatTrackData(currentTrack, true);
    } else {
      // If nothing is currently playing, get the most recent track
      const recentTrack = await getRecentlyPlayedTrack(accessToken);
      if (recentTrack) {
        formattedTrack = formatTrackData(recentTrack, false);
      }
    }

    if (!formattedTrack) {
      return res.status(404).json({ error: 'No recently played tracks found' });
    }

    // Update cache
    trackCache = {
      data: formattedTrack,
      timestamp: Date.now()
    };

    res.json(formattedTrack);
  } catch (error) {
    console.error('Error in Spotify API:', error);
    res.status(500).json({ error: 'Failed to fetch Spotify track data' });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

app.listen(PORT, () => {
  console.log(`🎵 Spotify API server running on http://localhost:${PORT}`);
  console.log(`🔗 Frontend should make requests to http://localhost:${PORT}/api/spotify`);
});
