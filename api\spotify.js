// Vercel serverless function for Spotify API
export default async function handler(req, res) {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Credentials', true);
  res.setHeader('Access-Control-Allow-Origin', 'https://ourudev-omega.vercel.app');
  res.setHeader('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  res.setHeader('Access-Control-Allow-Headers', 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Get access token
    const accessToken = await refreshAccessToken();
    
    // Try to get currently playing track first
    let currentTrack = await getCurrentlyPlaying(accessToken);
    let formattedTrack = null;
    
    if (currentTrack && currentTrack.item) {
      formattedTrack = formatTrackData(currentTrack, true);
    } else {
      // If nothing is currently playing, get the most recent track
      const recentTrack = await getRecentlyPlayedTrack(accessToken);
      if (recentTrack) {
        formattedTrack = formatTrackData(recentTrack, false);
      }
    }

    if (!formattedTrack) {
      return res.status(404).json({ error: 'No recently played tracks found' });
    }

    // Cache headers
    res.setHeader('Cache-Control', 's-maxage=300, stale-while-revalidate');
    
    res.status(200).json(formattedTrack);
  } catch (error) {
    console.error('Error in Spotify API:', error);
    res.status(500).json({ error: 'Failed to fetch Spotify track data' });
  }
}

// Refresh Spotify access token
async function refreshAccessToken() {
  const refresh_token = process.env.SPOTIFY_REFRESH_TOKEN;
  const client_id = process.env.SPOTIFY_CLIENT_ID;
  const client_secret = process.env.SPOTIFY_CLIENT_SECRET;

  if (!refresh_token || !client_id || !client_secret) {
    throw new Error('Missing Spotify credentials in environment variables');
  }

  const response = await fetch('https://accounts.spotify.com/api/token', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${Buffer.from(`${client_id}:${client_secret}`).toString('base64')}`
    },
    body: new URLSearchParams({
      grant_type: 'refresh_token',
      refresh_token: refresh_token
    })
  });

  if (!response.ok) {
    throw new Error(`Failed to refresh token: ${response.statusText}`);
  }

  const data = await response.json();
  return data.access_token;
}

// Get recently played track from Spotify
async function getRecentlyPlayedTrack(accessToken) {
  const response = await fetch('https://api.spotify.com/v1/me/player/recently-played?limit=1', {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });

  if (!response.ok) {
    if (response.status === 401) {
      // Token expired, try refreshing
      const newToken = await refreshAccessToken();
      return getRecentlyPlayedTrack(newToken);
    }
    throw new Error(`Failed to fetch recently played tracks: ${response.statusText}`);
  }

  const data = await response.json();
  return data.items[0] || null;
}

// Get current playing track from Spotify
async function getCurrentlyPlaying(accessToken) {
  const response = await fetch('https://api.spotify.com/v1/me/player/currently-playing', {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });

  if (response.status === 204) {
    // No track currently playing
    return null;
  }

  if (!response.ok) {
    if (response.status === 401) {
      // Token expired, try refreshing
      const newToken = await refreshAccessToken();
      return getCurrentlyPlaying(newToken);
    }
    throw new Error(`Failed to fetch currently playing track: ${response.statusText}`);
  }

  const data = await response.json();
  return data;
}

// Format track data for frontend
function formatTrackData(track, isCurrentlyPlaying = false) {
  if (!track) return null;

  const trackItem = isCurrentlyPlaying ? track.item : track.track;
  
  return {
    title: trackItem.name,
    artist: trackItem.artists.map(artist => artist.name).join(', '),
    album: trackItem.album.name,
    albumArt: trackItem.album.images[0]?.url || null,
    spotifyUrl: trackItem.external_urls.spotify,
    isPlaying: isCurrentlyPlaying && track.is_playing,
    playedAt: isCurrentlyPlaying ? new Date().toISOString() : track.played_at
  };
}
