# Deployment Guide for Vercel

## Prerequisites

1. **Spotify App Setup**:

   - Go to https://developer.spotify.com/dashboard/
   - Create a new app
   - Add redirect URI: `http://localhost:8888/callback`
   - Note your Client ID and Client Secret

2. **Environment Variables**:
   - Copy `.env.local.example` to `.env.local`
   - Fill in your Spotify credentials

## Local Setup

1. **Install dependencies**:

   ```bash
   npm install
   ```

2. **Run Spotify setup**:

   ```bash
   npm run setup-spotify
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

## Vercel Deployment

### Method 1: Using Vercel Dashboard (Recommended)

1. **Connect to Vercel**:

   - Go to https://vercel.com/
   - Import your GitHub repository
   - Choose your project

2. **Set Environment Variables**:

   - In your Vercel project dashboard
   - Go to Settings > Environment Variables
   - Add these variables for all environments (Production, Preview, Development):
     - `SPOTIFY_CLIENT_ID`: Your Spotify Client ID
     - `SPOTIFY_CLIENT_SECRET`: Your Spotify Client Secret
     - `SPOTIFY_REFRESH_TOKEN`: Generated from setup script

3. **Deploy**:
   - Vercel will automatically deploy when you push to your main branch
   - Or trigger a manual deployment from the dashboard

### Method 2: Using Vercel CLI

1. **Install Vercel CLI**:

   ```bash
   npm i -g vercel
   ```

2. **Login and Link Project**:

   ```bash
   vercel login
   vercel link
   ```

3. **Add Environment Variables via CLI**:

   ```bash
   vercel env add SPOTIFY_CLIENT_ID
   vercel env add SPOTIFY_CLIENT_SECRET
   vercel env add SPOTIFY_REFRESH_TOKEN
   ```

   (Enter the values when prompted)

4. **Deploy**:
   ```bash
   vercel --prod
   ```

## Domain Configuration

Your site will be available at: `https://ourudev-omega.vercel.app/`

The Spotify integration will automatically work with:

- API endpoint: `https://ourudev-omega.vercel.app/api/spotify`
- Proper CORS configuration for your domain

## Troubleshooting

1. **Spotify API Issues**:

   - Ensure all environment variables are set in Vercel
   - Check that your refresh token is valid
   - Verify redirect URI matches exactly in Spotify Dashboard

2. **Build Issues**:

   - Make sure all dependencies are installed
   - Check that TypeScript files compile correctly

3. **API Endpoint Issues**:
   - Verify the `/api/spotify.js` file is in the correct location
   - Check Vercel function logs for errors

## Features

- ✅ Spotify integration with real-time track display
- ✅ Responsive design
- ✅ Dark/light theme support
- ✅ Serverless API functions
- ✅ Automatic token refresh
- ✅ Caching for performance
