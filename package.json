{"name": "vite-react-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc & vite build", "lint": "eslint .", "preview": "vite preview", "setup-spotify": "ts-node -P tsconfig.script.json scripts/setup-spotify.ts", "api": "node server/spotify-api.js", "dev:full": "concurrently \"npm run api\" \"npm run dev\""}, "dependencies": {"@radix-ui/react-hover-card": "^1.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toggle": "^1.1.2", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "express": "^5.1.0", "framer-motion": "^12.4.10", "lucide-react": "^0.475.0", "motion": "^12.4.10", "open": "^10.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "swr": "^2.3.4", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.2"}, "devDependencies": {"@eslint/js": "^9.20.0", "@types/express": "^5.0.3", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "concurrently": "^9.2.0", "eslint": "^9.20.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.5.2", "tailwindcss": "^3.4.17", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2", "vite": "^6.1.0"}}