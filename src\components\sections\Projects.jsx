import { Badge } from "../ui/badge"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../ui/card"

const ProjectCard = ({ title, description, image, techStack, repoLink, liveLink }) => {
  return (
    <Card className="group transition-all duration-300 hover:shadow-lg">
      <CardHeader>
        <div className="aspect-video w-full overflow-hidden rounded-lg mb-4">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          />
        </div>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-2 mb-4">
          {techStack.map((tech, index) => (
            <Badge key={index} variant="outline">
              {tech}
            </Badge>
          ))}
        </div>
        <div className="flex gap-2">
          <Badge asChild variant="secondary" className="hover:bg-primary hover:text-primary-foreground">
            <a href={repoLink} target="_blank" rel="noopener noreferrer">
              Repository
            </a>
          </Badge>
          {liveLink && (
          <Badge asChild variant="secondary" className="hover:bg-primary hover:text-primary-foreground">
            <a href={liveLink} target="_blank" rel="noopener noreferrer">
              Live Demo
            </a>
          </Badge>
        )}
        </div>
      </CardContent>
    </Card>
  )
}

const Projects = () => {
  const projects = [
    {
      title: "ORON: Organization platform",
      description: "A dynamic web app for Ongata Rongai Organization For The Needy. A seamless fullstack platform to manage and collect donations for the valnurable. ",
      image: "/project2.png",
      techStack: ["React", "Tailwind", "Shadcn UI", "MongoDB", "Firebase", "NodeJS", "Express"],
      repoLink: "https://github.com/ourusheldon",
      liveLink: "https://www.oron.co.ke"
    },
    {
      title: "PMS: Career Guidance Platform",
      description: "A plartform empowering migrants achieve their dreams seamlessly. The plartform to seek career guidance and build performing startups.",
      image: "/project1.png",
      techStack: ["React", "Tailwind", "MongoDB",  "NodeJS", "Express"],
      repoLink: "https://github.com/ourusheldon",
      liveLink: "https://ourudev-omega.vercel.app/#"
    },
    {
      title: "M-pesa Sim Registration",
      description: "Kenya's top mobile money platform. A seamless ecomerce site to order, buy and register M-pesa sim cards and have it delivered with ease.",
      image: "/project3.png",
      techStack: ["NextJS", "Tailwind", "Solidity", "Hardhat", "React", "NodeJS", "Blockchain"],
      repoLink: "https://github.com/ourusheldon",
      liveLink: "https://mpesa-web-psi.vercel.app/"
    },
    {
      title: "Event Manager App",
      description: "This is an Event Management Application that allows users to create, manage, and participate in events. The application provides a user-friendly interface to handle all event-related activities",
      image: "/eventmanager.png",
      techStack: ["React","Tailwind", "Node.js", "Firebase"],
      repoLink: "https://github.com/ourusheldon",
      liveLink: "https://ouru-dev.vercel.app/"
    },
    
    
  ]

  return (
    <section className="py-20 px-4 md:px-8 lg:px-16">
      <h2 className="text-3xl font-bold mb-4">My Projects</h2>
      <p className="text-muted-foreground mb-8">
        Check out my latest work. I've worked on a variety of projects, from simple websites to complex applications. Here are a few of my favorites.
      </p>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {projects.map((project, index) => (
          <ProjectCard key={index} {...project} />
        ))}
      </div>
    </section>
  )
}

export default Projects
